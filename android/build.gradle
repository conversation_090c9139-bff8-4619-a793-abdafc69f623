buildscript {
    ext.kotlin_version = '2.0.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.2'
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // END: FlutterFire Configuration
        // Add the dependency for the App Distribution Gradle plugin
        classpath("com.google.firebase:firebase-appdistribution-gradle:4.0.0")
//        runtimeOnly 'org.jetbrains.kotlin:kotlinx-metadata-jvm:0.9.0'
    }
}


allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // This code is where all the magic happens and fixes the error.
    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace project.group
                    }
                }
            }
//            if (project.hasProperty("kotlin")) {
//                project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
//                    kotlinOptions {
//                        jvmTarget = "17"
//                    }
//                }
//                project.dependencies {
//                    // Force the kotlin version for the pdfx project
//                    implementation "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24"
//                }
//            }
        }
    }
    // This code is where all the magic happens and fixes the error.
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'com.android.support'
                    && !details.requested.name.contains('multidex') ) {
                details.useVersion "27.1.1"
            }
        }
    }
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
