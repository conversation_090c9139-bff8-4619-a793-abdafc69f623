import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:diogeneschatbot/models/chat_node.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/large_language_model.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/static/logger.dart';
import 'package:diogeneschatbot/models/enums/large_language_model_type.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:llama_sdk/llama_sdk.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LlamaCppModel extends LargeLanguageModel {
  @override
  LargeLanguageModelType get type => LargeLanguageModelType.llamacpp;

  Llama? _llama;

  String _template = '';

  bool _downloading = false;

  String get template => _template;

  set template(String value) {
    _template = value;
    notifyListeners();
  }

  @override
  List<String> get missingRequirements {
    List<String> missing = [];

    if (uri.isEmpty) {
      missing.add('- A path to the model file is required.\n');
    }

    if (!File(uri).existsSync()) {
      missing.add('- The file provided does not exist.\n');
    }

    if (_downloading) {
      missing.add('- The model is currently downloading.\n');
    }

    return missing;
  }

  static LlamaCppModel of(BuildContext context) =>
      LargeLanguageModel.of(context) as LlamaCppModel;

  LlamaCppModel({
    super.listener,
    super.name,
    super.uri,
    super.useDefault,
    super.penalizeNewline,
    super.seed,
    super.nKeep,
    super.nPredict = 512,
    super.topK,
    super.topP,
    super.minP,
    super.tfsZ,
    super.typicalP,
    super.temperature,
    super.penaltyLastN,
    super.penaltyRepeat,
    super.penaltyPresent,
    super.penaltyFreq,
    super.mirostat,
    super.mirostatTau,
    super.mirostatEta,
    super.nCtx = 0,
    super.nBatch,
    super.nThread,
    String template = '',
  }) {
    if (uri.isNotEmpty && Directory(uri).existsSync()) {
      _llama = Llama(toLlamaController());
    }

    _template = template;
  }

  LlamaCppModel.fromMap(VoidCallback listener, Map<String, dynamic> json) {
    addListener(listener);
    fromMap(json);
  }

  @override
  void fromMap(Map<String, dynamic> json) {
    super.fromMap(json);
    _template = json['template'] ?? '';
    nPredict = json['nPredict'] ?? 512;
    nCtx = json['nCtx'] ?? 0;
    notifyListeners();
  }

  @override
  Map<String, dynamic> toMap() {
    return {...super.toMap(), 'template': _template};
  }

  LlamaController toLlamaController() {
    if (uri.isEmpty) {
      throw Exception("Model URI is empty");
    }

    return LlamaController(
      modelPath: uri,
      nCtx: nCtx,
      nBatch: nBatch,
      greedy: temperature == 0.0, // Use greedy sampling when temperature is 0
      temperature: temperature,
      topK: topK,
      topP: topP,
      seed: seed != 0 ? seed : null,
      nThreads: nThread,
      nThreadsBatch: nThread,
    );
  }

  Future<String> loadModel() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        dialogTitle: "Load Model File",
        type: FileType.any,
        allowMultiple: false,
        allowCompression: false,
      );

      File file;
      if (result != null && result.files.isNotEmpty) {
        Logger.log("File selected: ${result.files.single.path}");
        file = File(result.files.single.path!);
      } else {
        Logger.log("No file selected");
        throw Exception("File is null");
      }

      Logger.log("Loading model from $file");

      uri = file.path;
      name = file.path.split('/').last;
      notifyListeners();
    } catch (e) {
      return e.toString();
    }

    return "Model Successfully Loaded";
  }

  @override
  Stream<String> prompt(List<ChatNode> messages) {
    try {
      _llama ??= Llama(toLlamaController());

      List<LlamaMessage> llamaMessages = [];

      for (var message in messages) {
        llamaMessages.add(message.toChatMessage());
      }

      return _llama!.prompt(llamaMessages);
    } catch (e) {
      Logger.log("Error initializing model: $e");
      return const Stream.empty();
    }
  }

  @override
  Future<void> save() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setString("llama_cpp_model", json.encode(toMap()));
  }

  void init() {
    try {
      _llama = Llama(toLlamaController());

      save();
    } catch (e) {
      Logger.log("Error initializing model: $e");
    }
  }

  void stop() async {
    _llama?.stop();
    _llama = null;
  }

  void setModelWithFuture(String filePath, String tag) async {
    _downloading = true;
    notifyListeners();

    try {
      uri = filePath;
      name = tag;
    } catch (e) {
      Logger.log("Error setting model: $e");
    }

    _downloading = false;
    notifyListeners();
  }

  @override
  Future<void> resetUri() async {
    uri = '';
    name = '';
    notifyListeners();
  }

  @override
  void reset() {
    fromMap({});
  }
}
