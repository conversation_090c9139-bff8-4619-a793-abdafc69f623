import 'package:avatar_glow/avatar_glow.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:diogeneschatbot/bloc/profile_bloc.dart';
import 'package:diogeneschatbot/features/payment/presentation/pages/balance_page.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/pages/contact_list_page.dart';
import 'package:diogeneschatbot/pages/follow_list_page.dart';
import 'package:diogeneschatbot/tool/image_picker.dart';
import 'package:diogeneschatbot/tool/image_picker_stub.dart'
    if (dart.library.io) 'package:diogeneschatbot/tool/image_picker_io.dart'
    if (dart.library.html) 'package:diogeneschatbot/tool/image_picker_web.dart';
import 'package:diogeneschatbot/widgets/post_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:random_avatar/random_avatar.dart';
import 'package:universal_io/io.dart';

class UserProfilePage extends StatefulWidget {
  final String userId;
  final bool showAppBar;

  UserProfilePage({required this.userId, this.showAppBar = false});

  @override
  _UserProfilePageState createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> {
  late ProfileBloc _profileBloc;
  CommonImagePicker imagePicker = CommonImagePicker();
  var scrollController = ScrollController();

  String? currentUserId;

  @override
  void initState() {
    super.initState();
    _initializeCurrentUserId();
  }

  Future<void> _initializeCurrentUserId() async {
    currentUserId = FirebaseAuth.instance.currentUser?.uid;
    _profileBloc = ProfileBloc(
      userId: widget.userId,
      currentUserId: currentUserId,
    );
    if (!await _profileBloc.profileRepository.isBlocked(
      currentUserId!,
      widget.userId,
    )) {
      _profileBloc.add(LoadProfile());
    }
    setState(() {});
  }

  @override
  void dispose() {
    _profileBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: widget.showAppBar
          ? AppBar(
              title: Text(
                'User Profile',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              backgroundColor: const Color(0xFF89B998),
              elevation: 0,
              leading: IconButton(
                icon: Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [const Color(0xFF89B998), const Color(0xFF4FAD85)],
                  ),
                ),
              ),
            )
          : null,
      body: BlocBuilder<ProfileBloc, ProfileState>(
        bloc: _profileBloc,
        builder: (context, state) {
          if (state is ProfileLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      const Color(0xFF89B998),
                    ),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading profile...',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            );
          } else if (state is ProfileLoaded) {
            return CustomScrollView(
              controller: scrollController,
              slivers: [
                _buildProfileHeader(state.profile, theme),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        _buildProfileInfo(state.profile),
                        SizedBox(height: 16),
                        if (kIsWeb)
                          PostsList(
                            posts: state.posts,
                            currentUserId: currentUserId,
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          } else {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: colorScheme.error),
                  SizedBox(height: 16),
                  Text(
                    'An error occurred',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: colorScheme.error,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Please try again later',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildProfileHeader(Profile profile, ThemeData theme) {
    return SliverAppBar(
      expandedHeight: 200.0,
      floating: false,
      pinned: false,
      automaticallyImplyLeading: false,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF89B998),
                const Color(0xFF4FAD85),
                const Color(0xFFA2E3F6).withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Subtle overlay pattern
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      center: Alignment.center,
                      radius: 1.0,
                      colors: [
                        Colors.white.withValues(alpha: 0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              // Profile avatar centered
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildEnhancedAvatar(profile),
                    SizedBox(height: 12),
                    Text(
                      profile.name.isNotEmpty ? profile.name : profile.username,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black.withValues(alpha: 0.3),
                          ),
                        ],
                      ),
                    ),
                    if (profile.bio.isNotEmpty) ...[
                      SizedBox(height: 4),
                      Text(
                        profile.bio,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedAvatar(Profile profile) {
    return Hero(
      tag: 'profile_avatar_${profile.id}',
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () async {
            if (currentUserId != null &&
                currentUserId!.isNotEmpty &&
                widget.userId == currentUserId!) {
              List<CommonImageData> imageData = await imagePicker.pickImages(
                context: context,
              );
              List<String> imageUrlList = await imagePicker.uploadImages(
                imageData,
                userId: profile.id,
              );
              if (imageUrlList.isNotEmpty) {
                String imageUrl = imageUrlList.first;
                Profile updatedProfile = profile.copyWith(imageUrl: imageUrl);
                _profileBloc.add(UpdateProfile(updatedProfile));
              }
            }
          },
          child: profile.imageUrl != null && profile.imageUrl!.isNotEmpty
              ? AvatarGlow(
                  startDelay: const Duration(milliseconds: 1000),
                  glowColor: Colors.white,
                  glowShape: BoxShape.circle,
                  curve: Curves.fastOutSlowIn,
                  child: CircleAvatar(
                    backgroundImage: CachedNetworkImageProvider(
                      profile.imageUrl!,
                    ),
                    radius: 60,
                    backgroundColor: Colors.white,
                  ),
                )
              : AvatarGlow(
                  startDelay: const Duration(milliseconds: 1000),
                  glowColor: Colors.white,
                  glowShape: BoxShape.circle,
                  curve: Curves.fastOutSlowIn,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                    padding: EdgeInsets.all(8),
                    child: RandomAvatar(profile.id, height: 104, width: 104),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo(Profile profile) {
    bool isFollowing = profile.followers.contains(currentUserId);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        // Profile Details Card
        _buildProfileDetailsCard(profile, theme, colorScheme),
        SizedBox(height: 16),

        // Statistics Cards
        _buildStatisticsSection(profile, theme, colorScheme),
        SizedBox(height: 16),

        // Balance Section (if current user)
        if (currentUserId != null && currentUserId == widget.userId)
          _buildEnhancedBalanceSection(profile, theme, colorScheme),

        // Action Buttons Section
        _buildActionButtonsSection(profile, isFollowing, theme, colorScheme),
        SizedBox(height: 16),

        // Contact Management Section
        _buildContactManagementSection(profile, theme, colorScheme),
      ],
    );
  }

  Widget _buildProfileDetailsCard(
    Profile profile,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      elevation: 8,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              colorScheme.surface,
              colorScheme.surface.withValues(alpha: 0.8),
            ],
          ),
        ),
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: const Color(0xFF89B998), size: 24),
                SizedBox(width: 8),
                Text(
                  'Profile Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            _buildEditableField('Name', profile.name, profile),
            _buildEditableField('Bio', profile.bio, profile),
            _buildInfoTile(
              'Username',
              profile.username,
              Icons.verified_user,
              theme,
              colorScheme,
              isVerified: true,
            ),
            _buildInfoTile(
              'Joined',
              '${_getMonthName(profile.createdAt.month)} ${profile.createdAt.year}',
              Icons.calendar_today,
              theme,
              colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(
    Profile profile,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Followers',
            profile.followers.length.toString(),
            Icons.people,
            const Color(0xFF89B998),
            () => _navigateToFollowList(profile.id, true),
            theme,
            colorScheme,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Following',
            profile.followings.length.toString(),
            Icons.people_outline,
            const Color(0xFF4FAD85),
            () => _navigateToFollowList(profile.id, false),
            theme,
            colorScheme,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Contacts',
            profile.contacts
                .where((contact) => contact.status == "confirmed")
                .length
                .toString(),
            Icons.contact_page,
            const Color(0xFFA2E3F6),
            currentUserId == widget.userId
                ? () => _navigateToContactList(profile)
                : null,
            theme,
            colorScheme,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableField(String label, String value, Profile profile) {
    return GestureDetector(
      child: ListTile(
        title: Text(label, style: TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(value.isEmpty ? 'No $label provided' : value),
        trailing: Icon(Icons.edit),
        onTap: () {
          if (currentUserId != null &&
              currentUserId!.isNotEmpty &&
              widget.userId == currentUserId!) {
            _editProfileField(context, profile, label, value);
          }
        },
      ),
    );
  }

  Widget _buildInfoTile(
    String title,
    String subtitle,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme, {
    bool isVerified = false,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: colorScheme.surface.withValues(alpha: 0.5),
      ),
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isVerified
                ? const Color(0xFF89B998)
                : colorScheme.primary.withValues(alpha: 0.1),
          ),
          child: Icon(
            icon,
            color: isVerified ? Colors.white : const Color(0xFF89B998),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing: isVerified
            ? Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF89B998).withValues(alpha: 0.1),
                ),
                child: Text(
                  'Verified',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: const Color(0xFF89B998),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : null,
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback? onTap,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: 0.2),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToFollowList(String userId, bool showFollowers) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            FollowListPage(userId: userId, showFollowers: showFollowers),
      ),
    );
  }

  void _navigateToContactList(Profile profile) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ContactListScreen(
          userId: widget.userId,
          currentUserId: currentUserId!,
          showAppBar: true,
        ),
      ),
    );
  }

  Widget _buildEnhancedBalanceSection(
    Profile profile,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Card(
      elevation: 6,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF89B998).withValues(alpha: 0.1),
              const Color(0xFF4FAD85).withValues(alpha: 0.05),
            ],
          ),
        ),
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF89B998).withValues(alpha: 0.2),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: const Color(0xFF89B998),
                    size: 24,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  'Wallet Balance',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Balance',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '\$${profile.balance.toStringAsFixed(2)}',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF89B998),
                      ),
                    ),
                  ],
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            BalancePage(userId: currentUserId!),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF89B998),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 3,
                  ),
                  icon: Icon(Icons.add, size: 20),
                  label: Text(
                    'Add Funds',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtonsSection(
    Profile profile,
    bool isFollowing,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    if (currentUserId == null || currentUserId!.isEmpty) {
      return SizedBox.shrink();
    }

    if (profile.id == currentUserId!) {
      return SizedBox.shrink(); // No action buttons for own profile
    }

    return Card(
      elevation: 4,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 12),
            Row(
              children: [
                // Contact Request Button
                if (!profile.contacts
                    .where((contact) => contact.status == "confirmed")
                    .map((contact) => contact.id)
                    .toSet()
                    .contains(currentUserId))
                  Expanded(
                    child: _buildActionButton(
                      'Send Request',
                      Icons.person_add,
                      const Color(0xFF89B998),
                      () {
                        _profileBloc.add(SendContactRequest(widget.userId));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text("Contact request sent")),
                        );
                      },
                      theme,
                    ),
                  ),
                if (!profile.contacts
                    .where((contact) => contact.status == "confirmed")
                    .map((contact) => contact.id)
                    .toSet()
                    .contains(currentUserId))
                  SizedBox(width: 12),

                // Follow/Unfollow Button (only for non-iOS/macOS)
                if (!Platform.isIOS && !Platform.isMacOS)
                  Expanded(
                    child: _buildActionButton(
                      isFollowing ? 'Unfollow' : 'Follow',
                      isFollowing
                          ? Icons.group_remove_outlined
                          : Icons.group_add_outlined,
                      isFollowing ? Colors.orange : const Color(0xFF4FAD85),
                      () {
                        _profileBloc.add(ToggleFollow());
                        setState(() {
                          isFollowing = !isFollowing;
                        });
                      },
                      theme,
                    ),
                  ),
                if (!Platform.isIOS && !Platform.isMacOS) SizedBox(width: 12),

                // Block Button
                Expanded(
                  child: _buildActionButton(
                    'Block',
                    Icons.block_outlined,
                    Colors.red,
                    () => _showBlockDialog(profile),
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
    ThemeData theme,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withValues(alpha: 0.3)),
        ),
        elevation: 0,
      ),
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
      ),
    );
  }

  void _showBlockDialog(Profile profile) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('Confirm Block'),
            ],
          ),
          content: Text(
            'Are you sure you want to block this contact? This action will prevent them from contacting you.',
          ),
          actions: [
            TextButton(
              child: Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Block'),
              onPressed: () async {
                _profileBloc.add(BlockContact(profile.id));
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildContactManagementSection(
    Profile profile,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    if (currentUserId == null ||
        currentUserId!.isEmpty ||
        currentUserId != widget.userId) {
      return SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.contacts, color: const Color(0xFF89B998), size: 24),
                SizedBox(width: 8),
                Text(
                  'Contact Management',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            _buildContactManagementTile(
              'Pending Requests',
              profile.contacts
                  .where((contact) => contact.status == "pending")
                  .length
                  .toString(),
              Icons.contact_page_outlined,
              const Color(0xFFFF9800),
              () => _navigateToContactListWithStatus(profile, {
                "pending",
              }, 'Pending Contact Request'),
              theme,
              colorScheme,
            ),
            SizedBox(height: 8),
            _buildContactManagementTile(
              'Confirmed Contacts',
              profile.contacts
                  .where((contact) => contact.status == "confirmed")
                  .length
                  .toString(),
              Icons.contact_page,
              const Color(0xFF89B998),
              () => _navigateToContactListWithStatus(profile, null, 'Contacts'),
              theme,
              colorScheme,
            ),
            SizedBox(height: 8),
            _buildContactManagementTile(
              'Blocked Users',
              profile.contacts
                  .where((contact) => contact.status == "blocked")
                  .length
                  .toString(),
              Icons.block_outlined,
              Colors.red,
              () => _navigateToContactListWithStatus(profile, {
                "blocked",
              }, 'BlackList'),
              theme,
              colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactManagementTile(
    String title,
    String count,
    IconData icon,
    Color color,
    VoidCallback onTap,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: color.withValues(alpha: 0.1),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: color.withValues(alpha: 0.2),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    '$count items',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: color.withValues(alpha: 0.2),
              ),
              child: Text(
                count,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToContactListWithStatus(
    Profile profile,
    Set<String>? statuses,
    String title,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ContactListScreen(
          title: title,
          userId: widget.userId,
          currentUserId: currentUserId!,
          showAppBar: true,
          statuses: statuses,
        ),
      ),
    );
  }

  Future<void> _editProfileField(
    BuildContext context,
    Profile profile,
    String fieldLabel,
    String fieldValue,
  ) async {
    TextEditingController fieldController = TextEditingController(
      text: fieldValue,
    );

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Edit $fieldLabel'),
          content: TextField(
            controller: fieldController,
            decoration: InputDecoration(
              hintText: 'Enter your $fieldLabel',
              border: OutlineInputBorder(),
            ),
            maxLength: 160,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (fieldLabel == 'Bio') {
                  profile = profile.copyWith(bio: fieldController.text);
                } else if (fieldLabel == 'Name') {
                  profile = profile.copyWith(name: fieldController.text);
                }
                _profileBloc.add(UpdateProfile(profile));
                Navigator.of(context).pop();
              },
              child: Text('Save'),
            ),
          ],
        );
      },
    );
  }
}
